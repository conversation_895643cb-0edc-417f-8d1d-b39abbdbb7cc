@echo off
echo Installing Advanced RPA Features...
echo.
echo This will install additional packages for:
echo - Advanced OCR (EasyOCR)
echo - Image processing (scikit-image)
echo - Data visualization (matplotlib)
echo.
echo Note: This may take several minutes and requires internet connection.
echo.

pause

REM Check if Python is available
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    goto install
)

python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    goto install
)

echo ERROR: Python not found
pause
exit /b 1

:install
echo Installing advanced dependencies...
echo.

echo [1/4] Installing EasyOCR...
%PYTHON_CMD% -m pip install easyocr
if errorlevel 1 (
    echo WARNING: EasyOCR installation failed
) else (
    echo EasyOCR installed successfully!
)

echo.
echo [2/4] Installing Tesseract OCR...
%PYTHON_CMD% -m pip install pytesseract
if errorlevel 1 (
    echo WARNING: Tesseract installation failed
) else (
    echo Tesseract installed successfully!
)

echo.
echo [3/4] Installing scikit-image...
%PYTHON_CMD% -m pip install scikit-image
if errorlevel 1 (
    echo WARNING: scikit-image installation failed
) else (
    echo scikit-image installed successfully!
)

echo.
echo [4/4] Installing matplotlib...
%PYTHON_CMD% -m pip install matplotlib
if errorlevel 1 (
    echo WARNING: matplotlib installation failed
) else (
    echo matplotlib installed successfully!
)

echo.
echo Advanced features installation completed!
echo You can now use all advanced RPA features.
echo.

pause
