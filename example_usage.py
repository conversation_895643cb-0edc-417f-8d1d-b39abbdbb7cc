#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA工具使用示例
"""

from rpa_core import RPACore
import time

def example_basic_usage():
    """基本使用示例"""
    print("=== RPA基本使用示例 ===")
    
    # 创建RPA实例
    rpa = RPACore()
    
    try:
        # 示例1: 录制操作
        print("1. 开始录制操作...")
        rpa.start_recording()
        
        print("请在5秒内执行一些操作（鼠标点击、键盘输入等）...")
        time.sleep(5)
        
        rpa.stop_recording()
        print(f"录制完成，共记录 {len(rpa.actions)} 个操作")
        
        # 示例2: 保存操作
        if rpa.actions:
            filename = "example_script.json"
            rpa.save_actions(filename)
            print(f"操作已保存到: {filename}")
        
        # 示例3: 播放操作
        if rpa.actions:
            print("3秒后开始播放录制的操作...")
            time.sleep(3)
            rpa.play_actions(repeat_count=1, speed_multiplier=1.0)
            print("播放完成")
        
    except KeyboardInterrupt:
        print("用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        # 清理资源
        if rpa.recording:
            rpa.stop_recording()
        if rpa.playing:
            rpa.stop_playing()

def example_load_and_play():
    """加载并播放脚本示例"""
    print("=== 加载并播放脚本示例 ===")
    
    rpa = RPACore()
    
    try:
        # 加载之前保存的脚本
        filename = "example_script.json"
        if rpa.load_actions(filename):
            print(f"成功加载脚本: {filename}")
            print(f"脚本包含 {len(rpa.actions)} 个操作")
            
            # 播放脚本
            print("3秒后开始播放脚本...")
            time.sleep(3)
            rpa.play_actions(repeat_count=2, speed_multiplier=0.5)  # 慢速播放2次
            print("播放完成")
        else:
            print(f"无法加载脚本: {filename}")
            
    except Exception as e:
        print(f"发生错误: {e}")

def example_image_recognition():
    """图像识别示例"""
    print("=== 图像识别示例 ===")
    
    rpa = RPACore()
    
    try:
        # 获取窗口列表
        windows = rpa.get_window_list()
        print("当前打开的窗口:")
        for i, (title, x, y, w, h) in enumerate(windows[:5]):  # 只显示前5个
            print(f"  {i+1}. {title} - 位置:({x},{y}) 大小:{w}x{h}")
        
        # 截取屏幕区域
        print("截取屏幕左上角 200x200 区域...")
        screenshot = rpa.capture_screen_region(0, 0, 200, 200)
        if screenshot is not None:
            print("截屏成功")
            # 这里可以保存截图或进行图像处理
        
    except Exception as e:
        print(f"发生错误: {e}")

def example_window_management():
    """窗口管理示例"""
    print("=== 窗口管理示例 ===")
    
    rpa = RPACore()
    
    try:
        # 获取所有窗口
        windows = rpa.get_window_list()
        
        if windows:
            print("找到以下窗口:")
            for i, (title, x, y, w, h) in enumerate(windows):
                if title.strip():  # 只显示有标题的窗口
                    print(f"  {i+1}. {title}")
            
            # 尝试激活第一个有效窗口
            for title, x, y, w, h in windows:
                if title.strip() and "RPA" not in title:  # 避免激活自己
                    print(f"尝试激活窗口: {title}")
                    if rpa.activate_window(title):
                        print("窗口激活成功")
                    else:
                        print("窗口激活失败")
                    break
        else:
            print("未找到可用窗口")
            
    except Exception as e:
        print(f"发生错误: {e}")

def main():
    """主函数"""
    print("RPA工具使用示例")
    print("=" * 50)
    
    while True:
        print("\n请选择示例:")
        print("1. 基本使用示例（录制和播放）")
        print("2. 加载并播放脚本")
        print("3. 图像识别示例")
        print("4. 窗口管理示例")
        print("0. 退出")
        
        choice = input("请输入选择 (0-4): ").strip()
        
        if choice == "1":
            example_basic_usage()
        elif choice == "2":
            example_load_and_play()
        elif choice == "3":
            example_image_recognition()
        elif choice == "4":
            example_window_management()
        elif choice == "0":
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
