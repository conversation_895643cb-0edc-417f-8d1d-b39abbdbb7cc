#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA高级功能模块 - 图像识别、文字识别、智能等待等
"""

import cv2
import numpy as np
import pyautogui
import time
import os
from PIL import Image, ImageDraw, ImageFont
import json
from typing import Tuple, List, Optional, Dict, Any
import threading
import queue


class ImageRecognition:
    """图像识别功能"""
    
    def __init__(self, confidence_threshold=0.8):
        self.confidence_threshold = confidence_threshold
        self.template_cache = {}
    
    def find_template(self, template_path: str, screenshot=None, confidence=None) -> Optional[Tuple[int, int, float]]:
        """在屏幕上查找模板图像"""
        if confidence is None:
            confidence = self.confidence_threshold
        
        try:
            # 获取屏幕截图
            if screenshot is None:
                screenshot = pyautogui.screenshot()
                screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # 加载模板图像
            if template_path in self.template_cache:
                template = self.template_cache[template_path]
            else:
                template = cv2.imread(template_path)
                if template is None:
                    print(f"无法加载模板图像: {template_path}")
                    return None
                self.template_cache[template_path] = template
            
            # 模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= confidence:
                # 计算中心点
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                return center_x, center_y, max_val
            
            return None
            
        except Exception as e:
            print(f"图像识别失败: {e}")
            return None
    
    def find_all_templates(self, template_path: str, screenshot=None, confidence=None) -> List[Tuple[int, int, float]]:
        """查找屏幕上所有匹配的模板图像"""
        if confidence is None:
            confidence = self.confidence_threshold
        
        try:
            if screenshot is None:
                screenshot = pyautogui.screenshot()
                screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            if template_path in self.template_cache:
                template = self.template_cache[template_path]
            else:
                template = cv2.imread(template_path)
                if template is None:
                    return []
                self.template_cache[template_path] = template
            
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            locations = np.where(result >= confidence)
            
            matches = []
            h, w = template.shape[:2]
            
            for pt in zip(*locations[::-1]):
                center_x = pt[0] + w // 2
                center_y = pt[1] + h // 2
                confidence_val = result[pt[1], pt[0]]
                matches.append((center_x, center_y, confidence_val))
            
            return matches
            
        except Exception as e:
            print(f"批量图像识别失败: {e}")
            return []
    
    def save_template(self, x: int, y: int, width: int, height: int, filename: str) -> bool:
        """保存屏幕区域作为模板"""
        try:
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            screenshot.save(filename)
            print(f"模板已保存: {filename}")
            return True
        except Exception as e:
            print(f"保存模板失败: {e}")
            return False
    
    def compare_images(self, img1_path: str, img2_path: str) -> float:
        """比较两个图像的相似度"""
        try:
            img1 = cv2.imread(img1_path)
            img2 = cv2.imread(img2_path)
            
            if img1 is None or img2 is None:
                return 0.0
            
            # 调整图像大小使其一致
            h1, w1 = img1.shape[:2]
            img2 = cv2.resize(img2, (w1, h1))
            
            # 计算相似度
            result = cv2.matchTemplate(img1, img2, cv2.TM_CCOEFF_NORMED)
            return float(result[0][0])
            
        except Exception as e:
            print(f"图像比较失败: {e}")
            return 0.0


class TextRecognition:
    """文字识别功能"""
    
    def __init__(self):
        self.ocr_engine = None
        self.setup_ocr()
    
    def setup_ocr(self):
        """设置OCR引擎"""
        try:
            import easyocr
            self.ocr_engine = easyocr.Reader(['ch_sim', 'en'])
            print("EasyOCR引擎初始化成功")
        except ImportError:
            print("EasyOCR未安装，将使用基础OCR功能")
            self.ocr_engine = None
    
    def extract_text_from_region(self, x: int, y: int, width: int, height: int) -> List[Dict[str, Any]]:
        """从屏幕区域提取文字"""
        try:
            # 截取指定区域
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            img_array = np.array(screenshot)
            
            if self.ocr_engine:
                # 使用EasyOCR
                results = self.ocr_engine.readtext(img_array)
                text_data = []
                
                for (bbox, text, confidence) in results:
                    # 计算文字在原始坐标系中的位置
                    bbox_array = np.array(bbox)
                    min_x = int(np.min(bbox_array[:, 0])) + x
                    min_y = int(np.min(bbox_array[:, 1])) + y
                    max_x = int(np.max(bbox_array[:, 0])) + x
                    max_y = int(np.max(bbox_array[:, 1])) + y
                    
                    text_data.append({
                        'text': text,
                        'confidence': confidence,
                        'bbox': (min_x, min_y, max_x, max_y),
                        'center': ((min_x + max_x) // 2, (min_y + max_y) // 2)
                    })
                
                return text_data
            else:
                # 基础OCR功能（需要安装tesseract）
                try:
                    import pytesseract
                    text = pytesseract.image_to_string(screenshot, lang='chi_sim+eng')
                    return [{'text': text.strip(), 'confidence': 0.5, 'bbox': (x, y, x+width, y+height), 'center': (x+width//2, y+height//2)}]
                except ImportError:
                    print("未安装OCR引擎")
                    return []
                    
        except Exception as e:
            print(f"文字识别失败: {e}")
            return []
    
    def find_text_on_screen(self, target_text: str, region=None) -> Optional[Tuple[int, int]]:
        """在屏幕上查找指定文字"""
        try:
            if region:
                x, y, width, height = region
            else:
                screen_size = pyautogui.size()
                x, y, width, height = 0, 0, screen_size.width, screen_size.height
            
            text_data = self.extract_text_from_region(x, y, width, height)
            
            for item in text_data:
                if target_text.lower() in item['text'].lower():
                    return item['center']
            
            return None
            
        except Exception as e:
            print(f"文字查找失败: {e}")
            return None
    
    def get_all_text_on_screen(self, region=None) -> List[Dict[str, Any]]:
        """获取屏幕上所有文字"""
        if region:
            x, y, width, height = region
        else:
            screen_size = pyautogui.size()
            x, y, width, height = 0, 0, screen_size.width, screen_size.height
        
        return self.extract_text_from_region(x, y, width, height)


class SmartWait:
    """智能等待功能"""
    
    def __init__(self):
        self.image_recognition = ImageRecognition()
        self.text_recognition = TextRecognition()
    
    def wait_for_image(self, template_path: str, timeout: int = 30, confidence: float = 0.8) -> Optional[Tuple[int, int]]:
        """等待图像出现"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.image_recognition.find_template(template_path, confidence=confidence)
            if result:
                return result[0], result[1]  # 返回坐标
            time.sleep(0.5)
        
        print(f"等待图像超时: {template_path}")
        return None
    
    def wait_for_text(self, target_text: str, timeout: int = 30, region=None) -> Optional[Tuple[int, int]]:
        """等待文字出现"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.text_recognition.find_text_on_screen(target_text, region)
            if result:
                return result
            time.sleep(0.5)
        
        print(f"等待文字超时: {target_text}")
        return None
    
    def wait_for_image_disappear(self, template_path: str, timeout: int = 30, confidence: float = 0.8) -> bool:
        """等待图像消失"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.image_recognition.find_template(template_path, confidence=confidence)
            if not result:
                return True
            time.sleep(0.5)
        
        print(f"等待图像消失超时: {template_path}")
        return False
    
    def wait_for_color_change(self, x: int, y: int, original_color: Tuple[int, int, int], timeout: int = 30) -> bool:
        """等待指定位置颜色变化"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            current_color = pyautogui.pixel(x, y)
            if current_color != original_color:
                return True
            time.sleep(0.1)
        
        print(f"等待颜色变化超时: ({x}, {y})")
        return False


class AdvancedActions:
    """高级操作功能"""
    
    def __init__(self):
        self.image_recognition = ImageRecognition()
        self.text_recognition = TextRecognition()
        self.smart_wait = SmartWait()
    
    def click_image(self, template_path: str, confidence: float = 0.8, timeout: int = 10) -> bool:
        """点击图像"""
        # 先等待图像出现
        location = self.smart_wait.wait_for_image(template_path, timeout, confidence)
        if location:
            pyautogui.click(location[0], location[1])
            print(f"点击图像成功: {template_path}")
            return True
        else:
            print(f"未找到图像: {template_path}")
            return False
    
    def click_text(self, target_text: str, timeout: int = 10, region=None) -> bool:
        """点击文字"""
        location = self.smart_wait.wait_for_text(target_text, timeout, region)
        if location:
            pyautogui.click(location[0], location[1])
            print(f"点击文字成功: {target_text}")
            return True
        else:
            print(f"未找到文字: {target_text}")
            return False
    
    def drag_image_to_image(self, source_template: str, target_template: str, confidence: float = 0.8) -> bool:
        """从一个图像拖拽到另一个图像"""
        source_location = self.image_recognition.find_template(source_template, confidence=confidence)
        target_location = self.image_recognition.find_template(target_template, confidence=confidence)
        
        if source_location and target_location:
            pyautogui.drag(source_location[0], source_location[1], 
                          target_location[0], target_location[1], 
                          duration=1.0)
            print(f"拖拽成功: {source_template} -> {target_template}")
            return True
        else:
            print(f"拖拽失败: 找不到源图像或目标图像")
            return False
    
    def type_text_smart(self, text: str, clear_first: bool = True) -> bool:
        """智能文字输入"""
        try:
            if clear_first:
                # 全选并清除
                pyautogui.hotkey('ctrl', 'a')
                time.sleep(0.1)
                pyautogui.press('delete')
                time.sleep(0.1)
            
            # 分段输入，避免输入过快
            for char in text:
                pyautogui.write(char)
                time.sleep(0.01)
            
            return True
        except Exception as e:
            print(f"智能输入失败: {e}")
            return False
    
    def scroll_to_find_image(self, template_path: str, max_scrolls: int = 10, scroll_direction: str = 'down') -> Optional[Tuple[int, int]]:
        """滚动查找图像"""
        for i in range(max_scrolls):
            result = self.image_recognition.find_template(template_path)
            if result:
                return result[0], result[1]
            
            # 滚动
            if scroll_direction == 'down':
                pyautogui.scroll(-3)
            elif scroll_direction == 'up':
                pyautogui.scroll(3)
            
            time.sleep(0.5)
        
        print(f"滚动查找图像失败: {template_path}")
        return None
    
    def conditional_action(self, condition_template: str, action_func, *args, **kwargs) -> bool:
        """条件执行操作"""
        if self.image_recognition.find_template(condition_template):
            return action_func(*args, **kwargs)
        return False


class TemplateManager:
    """模板管理器"""
    
    def __init__(self, templates_dir: str = "templates"):
        self.templates_dir = templates_dir
        os.makedirs(templates_dir, exist_ok=True)
        self.templates = {}
        self.load_templates()
    
    def load_templates(self):
        """加载所有模板"""
        try:
            for filename in os.listdir(self.templates_dir):
                if filename.endswith(('.png', '.jpg', '.jpeg')):
                    name = os.path.splitext(filename)[0]
                    path = os.path.join(self.templates_dir, filename)
                    self.templates[name] = path
            print(f"加载了 {len(self.templates)} 个模板")
        except Exception as e:
            print(f"加载模板失败: {e}")
    
    def save_template(self, name: str, x: int, y: int, width: int, height: int) -> bool:
        """保存新模板"""
        try:
            filename = f"{name}.png"
            filepath = os.path.join(self.templates_dir, filename)
            
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            screenshot.save(filepath)
            
            self.templates[name] = filepath
            print(f"模板已保存: {name}")
            return True
        except Exception as e:
            print(f"保存模板失败: {e}")
            return False
    
    def get_template_path(self, name: str) -> Optional[str]:
        """获取模板路径"""
        return self.templates.get(name)
    
    def list_templates(self) -> List[str]:
        """列出所有模板"""
        return list(self.templates.keys())
    
    def delete_template(self, name: str) -> bool:
        """删除模板"""
        if name in self.templates:
            try:
                os.remove(self.templates[name])
                del self.templates[name]
                print(f"模板已删除: {name}")
                return True
            except Exception as e:
                print(f"删除模板失败: {e}")
                return False
        return False
