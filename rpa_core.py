#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA自动化操作核心模块
"""

import time
import json
import os
from datetime import datetime
import pyautogui
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
from pynput import mouse, keyboard as pynput_keyboard
import psutil
import pygetwindow as gw
from rpa_advanced import ImageRecognition, TextRecognition, SmartWait, AdvancedActions, TemplateManager


class RPACore:
    def __init__(self):
        # 设置PyAutoGUI安全设置
        pyautogui.FAILSAFE = True  # 鼠标移到左上角停止
        pyautogui.PAUSE = 0.1  # 每次操作间隔

        self.recording = False
        self.playing = False
        self.actions = []
        self.current_action_index = 0

        # 录制相关
        self.mouse_listener = None
        self.keyboard_listener = None
        self.last_mouse_time = 0
        self.mouse_move_threshold = 0.5  # 鼠标移动记录阈值（秒）

        # 高级功能模块
        self.image_recognition = ImageRecognition()
        self.text_recognition = TextRecognition()
        self.smart_wait = SmartWait()
        self.advanced_actions = AdvancedActions()
        self.template_manager = TemplateManager()

        # 图像识别相关
        self.template_images = {}
        self.confidence_threshold = 0.8
        
    def start_recording(self):
        """开始录制操作"""
        if self.recording:
            return False
            
        self.recording = True
        self.actions = []
        print("开始录制操作...")
        
        # 启动鼠标监听
        self.mouse_listener = mouse.Listener(
            on_click=self._on_mouse_click,
            on_scroll=self._on_mouse_scroll,
            on_move=self._on_mouse_move
        )
        
        # 启动键盘监听
        self.keyboard_listener = pynput_keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release
        )
        
        self.mouse_listener.start()
        self.keyboard_listener.start()
        
        return True
    
    def stop_recording(self):
        """停止录制操作"""
        if not self.recording:
            return False
            
        self.recording = False
        print(f"录制完成，共记录 {len(self.actions)} 个操作")
        
        if self.mouse_listener:
            self.mouse_listener.stop()
            self.mouse_listener = None
            
        if self.keyboard_listener:
            self.keyboard_listener.stop()
            self.keyboard_listener = None
            
        return True
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if not self.recording:
            return
            
        action = {
            'type': 'mouse_click',
            'x': x,
            'y': y,
            'button': str(button),
            'pressed': pressed,
            'timestamp': time.time()
        }
        self.actions.append(action)
        print(f"记录鼠标点击: ({x}, {y}) {button} {'按下' if pressed else '释放'}")
    
    def _on_mouse_scroll(self, x, y, dx, dy):
        """鼠标滚轮事件"""
        if not self.recording:
            return
            
        action = {
            'type': 'mouse_scroll',
            'x': x,
            'y': y,
            'dx': dx,
            'dy': dy,
            'timestamp': time.time()
        }
        self.actions.append(action)
        print(f"记录鼠标滚轮: ({x}, {y}) dx={dx} dy={dy}")
    
    def _on_mouse_move(self, x, y):
        """鼠标移动事件"""
        if not self.recording:
            return
            
        current_time = time.time()
        if current_time - self.last_mouse_time < self.mouse_move_threshold:
            return
            
        self.last_mouse_time = current_time
        action = {
            'type': 'mouse_move',
            'x': x,
            'y': y,
            'timestamp': current_time
        }
        self.actions.append(action)
    
    def _on_key_press(self, key):
        """键盘按下事件"""
        if not self.recording:
            return
            
        try:
            key_name = key.char
        except AttributeError:
            key_name = str(key)
            
        action = {
            'type': 'key_press',
            'key': key_name,
            'timestamp': time.time()
        }
        self.actions.append(action)
        print(f"记录按键: {key_name}")
    
    def _on_key_release(self, key):
        """键盘释放事件"""
        if not self.recording:
            return
            
        try:
            key_name = key.char
        except AttributeError:
            key_name = str(key)
            
        action = {
            'type': 'key_release',
            'key': key_name,
            'timestamp': time.time()
        }
        self.actions.append(action)
    
    def play_actions(self, repeat_count=1, speed_multiplier=1.0):
        """播放录制的操作"""
        if not self.actions:
            print("没有可播放的操作")
            return False
            
        if self.playing:
            print("正在播放中...")
            return False
            
        self.playing = True
        
        try:
            for repeat in range(repeat_count):
                print(f"开始第 {repeat + 1} 次播放...")
                
                start_time = None
                for i, action in enumerate(self.actions):
                    if not self.playing:  # 检查是否被停止
                        break
                        
                    self.current_action_index = i
                    
                    # 计算延迟时间
                    if start_time is None:
                        start_time = action['timestamp']
                        delay = 0
                    else:
                        delay = (action['timestamp'] - start_time) / speed_multiplier
                        
                    if delay > 0:
                        time.sleep(delay)
                    
                    # 执行操作
                    self._execute_action(action)
                    start_time = action['timestamp']
                
                if repeat < repeat_count - 1:
                    time.sleep(1)  # 重复间隔
                    
        except Exception as e:
            print(f"播放过程中出错: {e}")
        finally:
            self.playing = False
            self.current_action_index = 0
            
        return True
    
    def _execute_action(self, action):
        """执行单个操作"""
        try:
            if action['type'] == 'mouse_click':
                if action['pressed']:
                    pyautogui.mouseDown(action['x'], action['y'], button=action['button'].split('.')[-1])
                else:
                    pyautogui.mouseUp(action['x'], action['y'], button=action['button'].split('.')[-1])
                    
            elif action['type'] == 'mouse_move':
                pyautogui.moveTo(action['x'], action['y'])
                
            elif action['type'] == 'mouse_scroll':
                pyautogui.scroll(action['dy'], x=action['x'], y=action['y'])
                
            elif action['type'] == 'key_press':
                key = action['key']
                if key.startswith('Key.'):
                    key = key.replace('Key.', '')
                pyautogui.keyDown(key)
                
            elif action['type'] == 'key_release':
                key = action['key']
                if key.startswith('Key.'):
                    key = key.replace('Key.', '')
                pyautogui.keyUp(key)
                
        except Exception as e:
            print(f"执行操作失败: {e}")
    
    def stop_playing(self):
        """停止播放"""
        self.playing = False
        print("停止播放")
    
    def save_actions(self, filename):
        """保存操作到文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.actions, f, ensure_ascii=False, indent=2)
            print(f"操作已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存失败: {e}")
            return False
    
    def load_actions(self, filename):
        """从文件加载操作"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.actions = json.load(f)
            print(f"从 {filename} 加载了 {len(self.actions)} 个操作")
            return True
        except Exception as e:
            print(f"加载失败: {e}")
            return False
    
    def capture_screen_region(self, x, y, width, height):
        """截取屏幕区域"""
        try:
            screenshot = pyautogui.screenshot(region=(x, y, width, height))
            return np.array(screenshot)
        except Exception as e:
            print(f"截屏失败: {e}")
            return None
    
    def find_image_on_screen(self, template_path, confidence=None):
        """在屏幕上查找图像"""
        if confidence is None:
            confidence = self.confidence_threshold
            
        try:
            location = pyautogui.locateOnScreen(template_path, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                return center.x, center.y
            return None
        except Exception as e:
            print(f"图像识别失败: {e}")
            return None
    
    def click_image(self, template_path, confidence=None):
        """点击屏幕上的图像"""
        location = self.find_image_on_screen(template_path, confidence)
        if location:
            pyautogui.click(location[0], location[1])
            return True
        return False
    
    def get_window_list(self):
        """获取所有窗口列表"""
        try:
            windows = gw.getAllWindows()
            return [(w.title, w.left, w.top, w.width, w.height) for w in windows if w.title]
        except Exception as e:
            print(f"获取窗口列表失败: {e}")
            return []
    
    def activate_window(self, window_title):
        """激活指定窗口"""
        try:
            windows = gw.getWindowsWithTitle(window_title)
            if windows:
                windows[0].activate()
                return True
            return False
        except Exception as e:
            print(f"激活窗口失败: {e}")
            return False

    # ========== 高级功能方法 ==========

    def click_image_smart(self, template_path_or_name, confidence=0.8, timeout=10):
        """智能点击图像"""
        # 如果是模板名称，获取路径
        if not template_path_or_name.endswith(('.png', '.jpg', '.jpeg')):
            template_path = self.template_manager.get_template_path(template_path_or_name)
            if not template_path:
                print(f"未找到模板: {template_path_or_name}")
                return False
        else:
            template_path = template_path_or_name

        return self.advanced_actions.click_image(template_path, confidence, timeout)

    def click_text_smart(self, target_text, timeout=10, region=None):
        """智能点击文字"""
        return self.advanced_actions.click_text(target_text, timeout, region)

    def wait_for_image_appear(self, template_path_or_name, timeout=30, confidence=0.8):
        """等待图像出现"""
        if not template_path_or_name.endswith(('.png', '.jpg', '.jpeg')):
            template_path = self.template_manager.get_template_path(template_path_or_name)
            if not template_path:
                return None
        else:
            template_path = template_path_or_name

        return self.smart_wait.wait_for_image(template_path, timeout, confidence)

    def wait_for_text_appear(self, target_text, timeout=30, region=None):
        """等待文字出现"""
        return self.smart_wait.wait_for_text(target_text, timeout, region)

    def extract_text_from_screen(self, x=None, y=None, width=None, height=None):
        """从屏幕提取文字"""
        if x is None:
            # 全屏
            region = None
        else:
            region = (x, y, width, height)

        return self.text_recognition.get_all_text_on_screen(region)

    def save_screen_template(self, name, x, y, width, height):
        """保存屏幕区域为模板"""
        return self.template_manager.save_template(name, x, y, width, height)

    def list_templates(self):
        """列出所有模板"""
        return self.template_manager.list_templates()

    def delete_template(self, name):
        """删除模板"""
        return self.template_manager.delete_template(name)

    def type_text_smart(self, text, clear_first=True):
        """智能文字输入"""
        return self.advanced_actions.type_text_smart(text, clear_first)

    def scroll_to_find_image(self, template_path_or_name, max_scrolls=10, direction='down'):
        """滚动查找图像"""
        if not template_path_or_name.endswith(('.png', '.jpg', '.jpeg')):
            template_path = self.template_manager.get_template_path(template_path_or_name)
            if not template_path:
                return None
        else:
            template_path = template_path_or_name

        return self.advanced_actions.scroll_to_find_image(template_path, max_scrolls, direction)

    def drag_image_to_image(self, source_template, target_template, confidence=0.8):
        """从一个图像拖拽到另一个图像"""
        # 处理模板名称
        if not source_template.endswith(('.png', '.jpg', '.jpeg')):
            source_path = self.template_manager.get_template_path(source_template)
            if not source_path:
                return False
        else:
            source_path = source_template

        if not target_template.endswith(('.png', '.jpg', '.jpeg')):
            target_path = self.template_manager.get_template_path(target_template)
            if not target_path:
                return False
        else:
            target_path = target_template

        return self.advanced_actions.drag_image_to_image(source_path, target_path, confidence)

    def find_all_images(self, template_path_or_name, confidence=0.8):
        """查找屏幕上所有匹配的图像"""
        if not template_path_or_name.endswith(('.png', '.jpg', '.jpeg')):
            template_path = self.template_manager.get_template_path(template_path_or_name)
            if not template_path:
                return []
        else:
            template_path = template_path_or_name

        return self.image_recognition.find_all_templates(template_path, confidence=confidence)

    def wait_for_color_change(self, x, y, timeout=30):
        """等待指定位置颜色变化"""
        original_color = pyautogui.pixel(x, y)
        return self.smart_wait.wait_for_color_change(x, y, original_color, timeout)
