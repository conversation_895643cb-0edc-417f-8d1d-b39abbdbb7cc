@echo off
echo Starting RPA Automation Tool...
echo.

REM Check if Python is available
py --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python via 'py' command
    set PYTHON_CMD=py
    goto install_deps
)

python --version >nul 2>&1
if not errorlevel 1 (
    echo Found Python via 'python' command
    set PYTHON_CMD=python
    goto install_deps
)

echo ERROR: Python not found. Please install Python 3.7+
pause
exit /b 1

:install_deps
echo Checking dependencies...
%PYTHON_CMD% -c "import pyautogui" >nul 2>&1
if errorlevel 1 (
    echo Installing basic dependencies...
    %PYTHON_CMD% -m pip install pyautogui pillow opencv-python pynput keyboard pygetwindow psutil
    if errorlevel 1 (
        echo ERROR: Failed to install basic dependencies
        pause
        exit /b 1
    )
)

echo Checking advanced dependencies...
%PYTHON_CMD% -c "import easyocr" >nul 2>&1
if errorlevel 1 (
    echo Installing advanced OCR dependencies (this may take a while)...
    %PYTHON_CMD% -m pip install easyocr pytesseract scikit-image matplotlib
    if errorlevel 1 (
        echo WARNING: Advanced OCR dependencies failed to install
        echo The tool will work with basic features only
    ) else (
        echo Advanced dependencies installed successfully!
    )
) else (
    echo All dependencies already installed
)

REM Create scripts directory
if not exist "rpa_scripts" mkdir rpa_scripts

REM Start the application
echo Starting RPA GUI...
%PYTHON_CMD% rpa_gui.py

if errorlevel 1 (
    echo Program encountered an error
)

pause
