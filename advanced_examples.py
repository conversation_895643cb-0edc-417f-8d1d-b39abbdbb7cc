#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA高级功能使用示例
"""

from rpa_core import RPACore
import time

def example_image_recognition():
    """图像识别示例"""
    print("=== 图像识别示例 ===")
    
    rpa = RPACore()
    
    try:
        # 1. 保存当前屏幕区域作为模板
        print("保存屏幕区域作为模板...")
        rpa.save_screen_template("test_button", 100, 100, 200, 50)
        
        # 2. 等待图像出现
        print("等待图像出现...")
        location = rpa.wait_for_image_appear("test_button", timeout=10)
        if location:
            print(f"图像出现在: {location}")
        else:
            print("图像未出现")
        
        # 3. 智能点击图像
        print("尝试点击图像...")
        success = rpa.click_image_smart("test_button")
        if success:
            print("点击成功")
        else:
            print("点击失败")
        
        # 4. 查找所有匹配的图像
        print("查找所有匹配的图像...")
        matches = rpa.find_all_images("test_button")
        print(f"找到 {len(matches)} 个匹配")
        for i, (x, y, confidence) in enumerate(matches):
            print(f"  匹配 {i+1}: 位置({x}, {y}) 置信度{confidence:.3f}")
            
    except Exception as e:
        print(f"图像识别示例出错: {e}")

def example_text_recognition():
    """文字识别示例"""
    print("=== 文字识别示例 ===")
    
    rpa = RPACore()
    
    try:
        # 1. 提取屏幕文字
        print("提取屏幕文字...")
        text_data = rpa.extract_text_from_screen(0, 0, 800, 600)
        
        print(f"识别到 {len(text_data)} 个文本区域:")
        for item in text_data[:5]:  # 只显示前5个
            print(f"  文字: '{item['text']}' 位置: {item['center']}")
        
        # 2. 等待特定文字出现
        print("等待文字'确定'出现...")
        location = rpa.wait_for_text_appear("确定", timeout=5)
        if location:
            print(f"文字出现在: {location}")
        else:
            print("文字未出现")
        
        # 3. 智能点击文字
        print("尝试点击文字...")
        success = rpa.click_text_smart("确定")
        if success:
            print("点击成功")
        else:
            print("点击失败")
            
    except Exception as e:
        print(f"文字识别示例出错: {e}")

def example_smart_automation():
    """智能自动化示例"""
    print("=== 智能自动化示例 ===")
    
    rpa = RPACore()
    
    try:
        # 模拟登录流程
        print("模拟智能登录流程...")
        
        # 1. 等待登录按钮出现并点击
        if rpa.wait_for_text_appear("登录", timeout=10):
            print("找到登录按钮，点击...")
            rpa.click_text_smart("登录")
            time.sleep(1)
        
        # 2. 智能输入用户名
        if rpa.wait_for_text_appear("用户名", timeout=5):
            print("找到用户名输入框...")
            rpa.click_text_smart("用户名")
            time.sleep(0.5)
            rpa.type_text_smart("test_user", clear_first=True)
        
        # 3. 智能输入密码
        if rpa.wait_for_text_appear("密码", timeout=5):
            print("找到密码输入框...")
            rpa.click_text_smart("密码")
            time.sleep(0.5)
            rpa.type_text_smart("test_password", clear_first=True)
        
        # 4. 点击提交按钮
        if rpa.wait_for_text_appear("提交", timeout=5):
            print("点击提交按钮...")
            rpa.click_text_smart("提交")
        
        # 5. 等待页面变化（颜色变化检测）
        print("等待页面响应...")
        color_changed = rpa.wait_for_color_change(400, 300, timeout=10)
        if color_changed:
            print("页面已响应")
        else:
            print("页面无响应")
            
    except Exception as e:
        print(f"智能自动化示例出错: {e}")

def example_advanced_operations():
    """高级操作示例"""
    print("=== 高级操作示例 ===")
    
    rpa = RPACore()
    
    try:
        # 1. 滚动查找图像
        print("滚动查找图像...")
        location = rpa.scroll_to_find_image("target_image", max_scrolls=5, direction='down')
        if location:
            print(f"滚动后找到图像: {location}")
        else:
            print("滚动后未找到图像")
        
        # 2. 拖拽操作
        print("执行拖拽操作...")
        success = rpa.drag_image_to_image("source_image", "target_image")
        if success:
            print("拖拽成功")
        else:
            print("拖拽失败")
        
        # 3. 模板管理
        print("模板管理操作...")
        templates = rpa.list_templates()
        print(f"当前有 {len(templates)} 个模板: {templates}")
        
        # 保存新模板
        rpa.save_screen_template("new_template", 200, 200, 100, 100)
        print("保存了新模板")
        
        # 删除模板
        if "test_template" in templates:
            rpa.delete_template("test_template")
            print("删除了测试模板")
            
    except Exception as e:
        print(f"高级操作示例出错: {e}")

def example_conditional_automation():
    """条件自动化示例"""
    print("=== 条件自动化示例 ===")
    
    rpa = RPACore()
    
    try:
        # 条件执行示例
        print("条件自动化流程...")
        
        # 检查是否有错误提示
        error_location = rpa.wait_for_text_appear("错误", timeout=2)
        if error_location:
            print("检测到错误提示，处理错误...")
            rpa.click_text_smart("确定")  # 关闭错误对话框
            time.sleep(1)
        
        # 检查是否需要登录
        login_location = rpa.wait_for_text_appear("请登录", timeout=2)
        if login_location:
            print("需要登录，执行登录流程...")
            # 这里可以调用登录函数
            example_smart_automation()
        else:
            print("已登录，继续其他操作...")
        
        # 根据不同状态执行不同操作
        if rpa.wait_for_text_appear("成功", timeout=5):
            print("操作成功，执行后续流程...")
            # 成功后的操作
        elif rpa.wait_for_text_appear("失败", timeout=5):
            print("操作失败，执行重试流程...")
            # 失败后的重试操作
        else:
            print("状态未知，等待更长时间...")
            time.sleep(5)
            
    except Exception as e:
        print(f"条件自动化示例出错: {e}")

def example_batch_operations():
    """批量操作示例"""
    print("=== 批量操作示例 ===")
    
    rpa = RPACore()
    
    try:
        # 批量处理多个相同元素
        print("批量处理示例...")
        
        # 查找所有"删除"按钮
        delete_buttons = rpa.find_all_images("delete_button")
        print(f"找到 {len(delete_buttons)} 个删除按钮")
        
        # 逐个点击（从后往前，避免位置变化）
        for i, (x, y, confidence) in enumerate(reversed(delete_buttons)):
            print(f"点击第 {len(delete_buttons)-i} 个删除按钮...")
            pyautogui.click(x, y)
            time.sleep(1)  # 等待界面更新
            
            # 确认删除
            if rpa.wait_for_text_appear("确认删除", timeout=3):
                rpa.click_text_smart("确定")
                time.sleep(1)
        
        print("批量删除完成")
        
        # 批量输入数据
        data_list = ["数据1", "数据2", "数据3"]
        for i, data in enumerate(data_list):
            print(f"输入第 {i+1} 项数据: {data}")
            
            # 找到输入框并输入
            if rpa.wait_for_text_appear("输入框", timeout=5):
                rpa.click_text_smart("输入框")
                time.sleep(0.5)
                rpa.type_text_smart(data, clear_first=True)
                
                # 按回车确认
                pyautogui.press('enter')
                time.sleep(1)
        
        print("批量输入完成")
        
    except Exception as e:
        print(f"批量操作示例出错: {e}")

def main():
    """主函数"""
    print("RPA高级功能示例")
    print("=" * 50)
    
    examples = [
        ("图像识别示例", example_image_recognition),
        ("文字识别示例", example_text_recognition),
        ("智能自动化示例", example_smart_automation),
        ("高级操作示例", example_advanced_operations),
        ("条件自动化示例", example_conditional_automation),
        ("批量操作示例", example_batch_operations),
    ]
    
    while True:
        print("\n请选择示例:")
        for i, (name, _) in enumerate(examples):
            print(f"{i+1}. {name}")
        print("0. 退出")
        
        try:
            choice = int(input("请输入选择 (0-{}): ".format(len(examples))))
            
            if choice == 0:
                print("退出程序")
                break
            elif 1 <= choice <= len(examples):
                print(f"\n执行: {examples[choice-1][0]}")
                print("-" * 30)
                examples[choice-1][1]()
                print("-" * 30)
                input("按回车继续...")
            else:
                print("无效选择")
                
        except ValueError:
            print("请输入有效数字")
        except KeyboardInterrupt:
            print("\n用户中断")
            break

if __name__ == "__main__":
    main()
