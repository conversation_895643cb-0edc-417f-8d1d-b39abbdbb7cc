#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RPA自动化操作GUI界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import time
import os
from datetime import datetime
from rpa_core import RPACore
import pyautogui


class RPAGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("RPA自动化操作工具")
        self.root.geometry("900x700")
        
        self.rpa_core = RPACore()
        self.update_thread = None
        self.running = True
        
        self.setup_ui()
        self.start_status_update()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 录制控制区域
        record_frame = ttk.LabelFrame(main_frame, text="录制控制", padding="10")
        record_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 录制按钮
        button_frame = ttk.Frame(record_frame)
        button_frame.pack(fill=tk.X)
        
        self.record_button = ttk.Button(button_frame, text="开始录制", command=self.start_recording)
        self.record_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_record_button = ttk.Button(button_frame, text="停止录制", command=self.stop_recording, state=tk.DISABLED)
        self.stop_record_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 录制状态
        self.record_status = tk.StringVar(value="未录制")
        ttk.Label(button_frame, textvariable=self.record_status).pack(side=tk.LEFT, padx=(20, 0))
        
        # 播放控制区域
        play_frame = ttk.LabelFrame(main_frame, text="播放控制", padding="10")
        play_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 播放设置
        settings_frame = ttk.Frame(play_frame)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(settings_frame, text="重复次数:").pack(side=tk.LEFT)
        self.repeat_var = tk.StringVar(value="1")
        repeat_spin = ttk.Spinbox(settings_frame, from_=1, to=100, width=10, textvariable=self.repeat_var)
        repeat_spin.pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(settings_frame, text="播放速度:").pack(side=tk.LEFT)
        self.speed_var = tk.StringVar(value="1.0")
        speed_spin = ttk.Spinbox(settings_frame, from_=0.1, to=5.0, increment=0.1, width=10, textvariable=self.speed_var)
        speed_spin.pack(side=tk.LEFT, padx=(5, 0))
        
        # 播放按钮
        play_button_frame = ttk.Frame(play_frame)
        play_button_frame.pack(fill=tk.X)
        
        self.play_button = ttk.Button(play_button_frame, text="开始播放", command=self.start_playing)
        self.play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_play_button = ttk.Button(play_button_frame, text="停止播放", command=self.stop_playing, state=tk.DISABLED)
        self.stop_play_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 播放状态
        self.play_status = tk.StringVar(value="未播放")
        ttk.Label(play_button_frame, textvariable=self.play_status).pack(side=tk.LEFT, padx=(20, 0))
        
        # 文件操作区域
        file_frame = ttk.LabelFrame(main_frame, text="文件操作", padding="10")
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        file_button_frame = ttk.Frame(file_frame)
        file_button_frame.pack(fill=tk.X)
        
        ttk.Button(file_button_frame, text="保存操作", command=self.save_actions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_button_frame, text="加载操作", command=self.load_actions).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(file_button_frame, text="清空操作", command=self.clear_actions).pack(side=tk.LEFT, padx=(0, 5))
        
        # 操作列表区域
        list_frame = ttk.LabelFrame(main_frame, text="操作列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建Treeview
        columns = ('序号', '类型', '详情', '时间')
        self.action_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        # 设置列标题
        for col in columns:
            self.action_tree.heading(col, text=col)
            
        # 设置列宽
        self.action_tree.column('序号', width=50)
        self.action_tree.column('类型', width=100)
        self.action_tree.column('详情', width=300)
        self.action_tree.column('时间', width=150)
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.action_tree.yview)
        self.action_tree.configure(yscrollcommand=scrollbar.set)
        
        self.action_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 高级功能区域
        advanced_frame = ttk.LabelFrame(main_frame, text="高级功能", padding="10")
        advanced_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行按钮
        advanced_row1 = ttk.Frame(advanced_frame)
        advanced_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(advanced_row1, text="图像识别测试", command=self.test_image_recognition).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(advanced_row1, text="文字识别测试", command=self.test_text_recognition).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(advanced_row1, text="模板管理", command=self.open_template_manager).pack(side=tk.LEFT, padx=(0, 5))

        # 第二行按钮
        advanced_row2 = ttk.Frame(advanced_frame)
        advanced_row2.pack(fill=tk.X)

        ttk.Button(advanced_row2, text="智能点击", command=self.smart_click_tool).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(advanced_row2, text="智能等待", command=self.smart_wait_tool).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(advanced_row2, text="脚本编辑器", command=self.open_script_editor).pack(side=tk.LEFT, padx=(0, 5))

        # 工具区域
        tool_frame = ttk.LabelFrame(main_frame, text="基础工具", padding="10")
        tool_frame.pack(fill=tk.X)

        tool_button_frame = ttk.Frame(tool_frame)
        tool_button_frame.pack(fill=tk.X)

        ttk.Button(tool_button_frame, text="截屏工具", command=self.open_screenshot_tool).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(tool_button_frame, text="鼠标位置", command=self.show_mouse_position).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(tool_button_frame, text="窗口列表", command=self.show_window_list).pack(side=tk.LEFT, padx=(0, 5))
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.pack(fill=tk.X, pady=(10, 0))
    
    def start_recording(self):
        """开始录制"""
        if self.rpa_core.start_recording():
            self.record_button.config(state=tk.DISABLED)
            self.stop_record_button.config(state=tk.NORMAL)
            self.record_status.set("录制中...")
            self.status_var.set("正在录制操作...")
            messagebox.showinfo("录制", "录制已开始！\n\n提示：\n- 移动鼠标到左上角可紧急停止\n- 点击'停止录制'按钮结束录制")
    
    def stop_recording(self):
        """停止录制"""
        if self.rpa_core.stop_recording():
            self.record_button.config(state=tk.NORMAL)
            self.stop_record_button.config(state=tk.DISABLED)
            self.record_status.set(f"已录制 {len(self.rpa_core.actions)} 个操作")
            self.status_var.set("录制完成")
            self.update_action_list()
    
    def start_playing(self):
        """开始播放"""
        if not self.rpa_core.actions:
            messagebox.showwarning("警告", "没有可播放的操作，请先录制或加载操作")
            return
        
        try:
            repeat_count = int(self.repeat_var.get())
            speed_multiplier = float(self.speed_var.get())
        except ValueError:
            messagebox.showerror("错误", "请输入有效的重复次数和播放速度")
            return
        
        self.play_button.config(state=tk.DISABLED)
        self.stop_play_button.config(state=tk.NORMAL)
        self.play_status.set("播放中...")
        self.status_var.set("正在播放操作...")
        
        # 在新线程中播放
        play_thread = threading.Thread(
            target=self._play_worker, 
            args=(repeat_count, speed_multiplier)
        )
        play_thread.daemon = True
        play_thread.start()
    
    def _play_worker(self, repeat_count, speed_multiplier):
        """播放工作线程"""
        try:
            self.rpa_core.play_actions(repeat_count, speed_multiplier)
        except Exception as e:
            messagebox.showerror("错误", f"播放过程中出错: {e}")
        finally:
            # 恢复UI状态
            self.root.after(0, self._play_finished)
    
    def _play_finished(self):
        """播放完成后的处理"""
        self.play_button.config(state=tk.NORMAL)
        self.stop_play_button.config(state=tk.DISABLED)
        self.play_status.set("播放完成")
        self.status_var.set("就绪")
    
    def stop_playing(self):
        """停止播放"""
        self.rpa_core.stop_playing()
        self._play_finished()
    
    def save_actions(self):
        """保存操作"""
        if not self.rpa_core.actions:
            messagebox.showwarning("警告", "没有可保存的操作")
            return
        
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            if self.rpa_core.save_actions(filename):
                messagebox.showinfo("成功", f"操作已保存到: {filename}")
    
    def load_actions(self):
        """加载操作"""
        filename = filedialog.askopenfilename(
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            if self.rpa_core.load_actions(filename):
                self.update_action_list()
                self.record_status.set(f"已加载 {len(self.rpa_core.actions)} 个操作")
                messagebox.showinfo("成功", f"从 {filename} 加载了 {len(self.rpa_core.actions)} 个操作")
    
    def clear_actions(self):
        """清空操作"""
        if messagebox.askyesno("确认", "确定要清空所有操作吗？"):
            self.rpa_core.actions = []
            self.update_action_list()
            self.record_status.set("未录制")
            self.status_var.set("操作已清空")
    
    def update_action_list(self):
        """更新操作列表"""
        # 清空现有项目
        for item in self.action_tree.get_children():
            self.action_tree.delete(item)
        
        # 添加新项目
        for i, action in enumerate(self.rpa_core.actions):
            action_type = action['type']
            
            # 格式化详情
            if action_type == 'mouse_click':
                detail = f"({action['x']}, {action['y']}) {action['button']} {'按下' if action['pressed'] else '释放'}"
            elif action_type == 'mouse_move':
                detail = f"移动到 ({action['x']}, {action['y']})"
            elif action_type == 'mouse_scroll':
                detail = f"滚轮 ({action['x']}, {action['y']}) dx={action['dx']} dy={action['dy']}"
            elif action_type in ['key_press', 'key_release']:
                detail = f"按键 {action['key']} {'按下' if action_type == 'key_press' else '释放'}"
            else:
                detail = str(action)
            
            # 格式化时间
            timestamp = datetime.fromtimestamp(action['timestamp']).strftime('%H:%M:%S.%f')[:-3]
            
            self.action_tree.insert('', 'end', values=(i+1, action_type, detail, timestamp))
    
    def open_screenshot_tool(self):
        """打开截屏工具"""
        screenshot_window = tk.Toplevel(self.root)
        screenshot_window.title("截屏工具")
        screenshot_window.geometry("400x300")
        
        ttk.Label(screenshot_window, text="截屏工具", font=("Arial", 14, "bold")).pack(pady=10)
        
        ttk.Button(screenshot_window, text="全屏截图", 
                  command=lambda: self.take_screenshot("fullscreen")).pack(pady=5)
        ttk.Button(screenshot_window, text="区域截图", 
                  command=lambda: self.take_screenshot("region")).pack(pady=5)
        
        # 显示当前鼠标位置
        pos_frame = ttk.Frame(screenshot_window)
        pos_frame.pack(pady=20)
        
        self.mouse_pos_var = tk.StringVar()
        ttk.Label(pos_frame, text="当前鼠标位置:").pack()
        ttk.Label(pos_frame, textvariable=self.mouse_pos_var, font=("Courier", 12)).pack()
        
        # 更新鼠标位置
        def update_mouse_pos():
            if screenshot_window.winfo_exists():
                x, y = pyautogui.position()
                self.mouse_pos_var.set(f"X: {x}, Y: {y}")
                screenshot_window.after(100, update_mouse_pos)
        
        update_mouse_pos()
    
    def take_screenshot(self, mode):
        """截屏"""
        try:
            if mode == "fullscreen":
                screenshot = pyautogui.screenshot()
                filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                screenshot.save(filename)
                messagebox.showinfo("成功", f"截图已保存: {filename}")
            elif mode == "region":
                messagebox.showinfo("提示", "请在3秒后选择截图区域...")
                time.sleep(3)
                # 这里可以添加区域选择功能
                messagebox.showinfo("提示", "区域截图功能开发中...")
        except Exception as e:
            messagebox.showerror("错误", f"截图失败: {e}")
    
    def show_mouse_position(self):
        """显示鼠标位置"""
        x, y = pyautogui.position()
        messagebox.showinfo("鼠标位置", f"当前鼠标位置: X={x}, Y={y}")
    
    def show_window_list(self):
        """显示窗口列表"""
        window_list_window = tk.Toplevel(self.root)
        window_list_window.title("窗口列表")
        window_list_window.geometry("600x400")
        
        # 创建列表框
        listbox_frame = ttk.Frame(window_list_window)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        listbox = tk.Listbox(listbox_frame)
        scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)
        
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 获取窗口列表
        windows = self.rpa_core.get_window_list()
        for title, x, y, w, h in windows:
            listbox.insert(tk.END, f"{title} ({x}, {y}, {w}x{h})")
        
        # 激活窗口按钮
        def activate_selected():
            selection = listbox.curselection()
            if selection:
                window_info = listbox.get(selection[0])
                title = window_info.split(" (")[0]
                if self.rpa_core.activate_window(title):
                    messagebox.showinfo("成功", f"已激活窗口: {title}")
                else:
                    messagebox.showerror("错误", f"无法激活窗口: {title}")
        
        ttk.Button(window_list_window, text="激活选中窗口", command=activate_selected).pack(pady=5)
    
    def start_status_update(self):
        """开始状态更新"""
        def update_status():
            if self.running:
                # 更新播放进度
                if self.rpa_core.playing and self.rpa_core.actions:
                    progress = f"{self.rpa_core.current_action_index + 1}/{len(self.rpa_core.actions)}"
                    self.play_status.set(f"播放中... ({progress})")
                
                self.root.after(500, update_status)
        
        update_status()
    
    def test_image_recognition(self):
        """测试图像识别功能"""
        test_window = tk.Toplevel(self.root)
        test_window.title("图像识别测试")
        test_window.geometry("500x400")

        ttk.Label(test_window, text="图像识别测试", font=("Arial", 14, "bold")).pack(pady=10)

        # 模板选择
        template_frame = ttk.Frame(test_window)
        template_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(template_frame, text="选择模板:").pack(side=tk.LEFT)
        template_var = tk.StringVar()
        template_combo = ttk.Combobox(template_frame, textvariable=template_var, width=30)
        template_combo['values'] = self.rpa_core.list_templates()
        template_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 置信度设置
        conf_frame = ttk.Frame(test_window)
        conf_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(conf_frame, text="置信度:").pack(side=tk.LEFT)
        conf_var = tk.StringVar(value="0.8")
        ttk.Entry(conf_frame, textvariable=conf_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 结果显示
        result_text = tk.Text(test_window, height=15, width=60)
        result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        def test_recognition():
            template_name = template_var.get()
            if not template_name:
                messagebox.showwarning("警告", "请选择一个模板")
                return

            try:
                confidence = float(conf_var.get())
                result = self.rpa_core.wait_for_image_appear(template_name, timeout=5, confidence=confidence)

                result_text.delete(1.0, tk.END)
                if result:
                    result_text.insert(tk.END, f"找到图像！\n位置: ({result[0]}, {result[1]})\n")

                    # 查找所有匹配
                    all_matches = self.rpa_core.find_all_images(template_name, confidence)
                    result_text.insert(tk.END, f"总共找到 {len(all_matches)} 个匹配:\n")
                    for i, (x, y, conf) in enumerate(all_matches):
                        result_text.insert(tk.END, f"  {i+1}. 位置:({x}, {y}) 置信度:{conf:.3f}\n")
                else:
                    result_text.insert(tk.END, "未找到图像")

            except ValueError:
                messagebox.showerror("错误", "置信度必须是数字")

        ttk.Button(test_window, text="开始识别", command=test_recognition).pack(pady=10)

    def test_text_recognition(self):
        """测试文字识别功能"""
        test_window = tk.Toplevel(self.root)
        test_window.title("文字识别测试")
        test_window.geometry("600x500")

        ttk.Label(test_window, text="文字识别测试", font=("Arial", 14, "bold")).pack(pady=10)

        # 区域选择
        region_frame = ttk.LabelFrame(test_window, text="识别区域", padding="10")
        region_frame.pack(fill=tk.X, padx=10, pady=5)

        region_vars = {}
        for label, default in [("X:", "0"), ("Y:", "0"), ("宽度:", "800"), ("高度:", "600")]:
            frame = ttk.Frame(region_frame)
            frame.pack(side=tk.LEFT, padx=5)
            ttk.Label(frame, text=label).pack()
            var = tk.StringVar(value=default)
            ttk.Entry(frame, textvariable=var, width=8).pack()
            region_vars[label[:-1]] = var

        # 全屏选项
        fullscreen_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(region_frame, text="全屏识别", variable=fullscreen_var).pack(side=tk.LEFT, padx=10)

        # 结果显示
        result_text = tk.Text(test_window, height=20, width=70)
        scrollbar = ttk.Scrollbar(test_window, orient="vertical", command=result_text.yview)
        result_text.configure(yscrollcommand=scrollbar.set)

        result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)

        def start_recognition():
            try:
                if fullscreen_var.get():
                    text_data = self.rpa_core.extract_text_from_screen()
                else:
                    x = int(region_vars["X"].get())
                    y = int(region_vars["Y"].get())
                    width = int(region_vars["宽度"].get())
                    height = int(region_vars["高度"].get())
                    text_data = self.rpa_core.extract_text_from_screen(x, y, width, height)

                result_text.delete(1.0, tk.END)
                result_text.insert(tk.END, f"识别到 {len(text_data)} 个文本区域:\n\n")

                for i, item in enumerate(text_data):
                    result_text.insert(tk.END, f"区域 {i+1}:\n")
                    result_text.insert(tk.END, f"  文字: {item['text']}\n")
                    result_text.insert(tk.END, f"  置信度: {item['confidence']:.3f}\n")
                    result_text.insert(tk.END, f"  位置: {item['bbox']}\n")
                    result_text.insert(tk.END, f"  中心: {item['center']}\n\n")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")
            except Exception as e:
                messagebox.showerror("错误", f"识别失败: {e}")

        ttk.Button(test_window, text="开始识别", command=start_recognition).pack(pady=10)

    def open_template_manager(self):
        """打开模板管理器"""
        manager_window = tk.Toplevel(self.root)
        manager_window.title("模板管理器")
        manager_window.geometry("700x500")

        # 模板列表
        list_frame = ttk.LabelFrame(manager_window, text="模板列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建列表框
        listbox_frame = ttk.Frame(list_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True)

        template_listbox = tk.Listbox(listbox_frame)
        list_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=template_listbox.yview)
        template_listbox.configure(yscrollcommand=list_scrollbar.set)

        template_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        list_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 刷新模板列表
        def refresh_templates():
            template_listbox.delete(0, tk.END)
            templates = self.rpa_core.list_templates()
            for template in templates:
                template_listbox.insert(tk.END, template)

        refresh_templates()

        # 操作按钮
        button_frame = ttk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def save_new_template():
            save_window = tk.Toplevel(manager_window)
            save_window.title("保存新模板")
            save_window.geometry("400x300")

            ttk.Label(save_window, text="模板名称:").pack(pady=5)
            name_var = tk.StringVar()
            ttk.Entry(save_window, textvariable=name_var, width=30).pack(pady=5)

            ttk.Label(save_window, text="截取区域:").pack(pady=(20, 5))

            coords_frame = ttk.Frame(save_window)
            coords_frame.pack(pady=5)

            coord_vars = {}
            for label in ["X", "Y", "宽度", "高度"]:
                frame = ttk.Frame(coords_frame)
                frame.pack(side=tk.LEFT, padx=5)
                ttk.Label(frame, text=f"{label}:").pack()
                var = tk.StringVar(value="100" if label in ["宽度", "高度"] else "0")
                ttk.Entry(frame, textvariable=var, width=8).pack()
                coord_vars[label] = var

            def save_template():
                name = name_var.get().strip()
                if not name:
                    messagebox.showwarning("警告", "请输入模板名称")
                    return

                try:
                    x = int(coord_vars["X"].get())
                    y = int(coord_vars["Y"].get())
                    width = int(coord_vars["宽度"].get())
                    height = int(coord_vars["高度"].get())

                    if self.rpa_core.save_screen_template(name, x, y, width, height):
                        messagebox.showinfo("成功", f"模板 '{name}' 已保存")
                        refresh_templates()
                        save_window.destroy()
                    else:
                        messagebox.showerror("错误", "保存模板失败")

                except ValueError:
                    messagebox.showerror("错误", "请输入有效的数字")

            ttk.Button(save_window, text="保存模板", command=save_template).pack(pady=20)

        def delete_selected():
            selection = template_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择要删除的模板")
                return

            template_name = template_listbox.get(selection[0])
            if messagebox.askyesno("确认", f"确定要删除模板 '{template_name}' 吗？"):
                if self.rpa_core.delete_template(template_name):
                    messagebox.showinfo("成功", "模板已删除")
                    refresh_templates()
                else:
                    messagebox.showerror("错误", "删除模板失败")

        ttk.Button(button_frame, text="保存新模板", command=save_new_template).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除模板", command=delete_selected).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="刷新列表", command=refresh_templates).pack(side=tk.LEFT, padx=(0, 5))

    def smart_click_tool(self):
        """智能点击工具"""
        click_window = tk.Toplevel(self.root)
        click_window.title("智能点击工具")
        click_window.geometry("400x300")

        ttk.Label(click_window, text="智能点击工具", font=("Arial", 14, "bold")).pack(pady=10)

        # 点击方式选择
        method_frame = ttk.LabelFrame(click_window, text="点击方式", padding="10")
        method_frame.pack(fill=tk.X, padx=10, pady=5)

        method_var = tk.StringVar(value="image")
        ttk.Radiobutton(method_frame, text="点击图像", variable=method_var, value="image").pack(anchor=tk.W)
        ttk.Radiobutton(method_frame, text="点击文字", variable=method_var, value="text").pack(anchor=tk.W)

        # 目标输入
        target_frame = ttk.LabelFrame(click_window, text="目标", padding="10")
        target_frame.pack(fill=tk.X, padx=10, pady=5)

        target_var = tk.StringVar()
        target_entry = ttk.Entry(target_frame, textvariable=target_var, width=40)
        target_entry.pack(fill=tk.X)

        # 设置
        settings_frame = ttk.LabelFrame(click_window, text="设置", padding="10")
        settings_frame.pack(fill=tk.X, padx=10, pady=5)

        timeout_frame = ttk.Frame(settings_frame)
        timeout_frame.pack(fill=tk.X)
        ttk.Label(timeout_frame, text="超时时间(秒):").pack(side=tk.LEFT)
        timeout_var = tk.StringVar(value="10")
        ttk.Entry(timeout_frame, textvariable=timeout_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        def execute_click():
            target = target_var.get().strip()
            if not target:
                messagebox.showwarning("警告", "请输入目标")
                return

            try:
                timeout = int(timeout_var.get())
                method = method_var.get()

                if method == "image":
                    success = self.rpa_core.click_image_smart(target, timeout=timeout)
                else:
                    success = self.rpa_core.click_text_smart(target, timeout=timeout)

                if success:
                    messagebox.showinfo("成功", f"点击成功: {target}")
                else:
                    messagebox.showerror("失败", f"点击失败: {target}")

            except ValueError:
                messagebox.showerror("错误", "超时时间必须是数字")

        ttk.Button(click_window, text="执行点击", command=execute_click).pack(pady=20)

    def smart_wait_tool(self):
        """智能等待工具"""
        wait_window = tk.Toplevel(self.root)
        wait_window.title("智能等待工具")
        wait_window.geometry("400x350")

        ttk.Label(wait_window, text="智能等待工具", font=("Arial", 14, "bold")).pack(pady=10)

        # 等待类型选择
        type_frame = ttk.LabelFrame(wait_window, text="等待类型", padding="10")
        type_frame.pack(fill=tk.X, padx=10, pady=5)

        wait_type_var = tk.StringVar(value="image")
        ttk.Radiobutton(type_frame, text="等待图像出现", variable=wait_type_var, value="image").pack(anchor=tk.W)
        ttk.Radiobutton(type_frame, text="等待文字出现", variable=wait_type_var, value="text").pack(anchor=tk.W)
        ttk.Radiobutton(type_frame, text="等待颜色变化", variable=wait_type_var, value="color").pack(anchor=tk.W)

        # 目标输入
        target_frame = ttk.LabelFrame(wait_window, text="等待目标", padding="10")
        target_frame.pack(fill=tk.X, padx=10, pady=5)

        target_var = tk.StringVar()
        ttk.Entry(target_frame, textvariable=target_var, width=40).pack(fill=tk.X)

        # 坐标输入（颜色变化用）
        coord_frame = ttk.Frame(target_frame)
        coord_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(coord_frame, text="坐标(颜色变化用) X:").pack(side=tk.LEFT)
        x_var = tk.StringVar(value="100")
        ttk.Entry(coord_frame, textvariable=x_var, width=8).pack(side=tk.LEFT, padx=(5, 10))

        ttk.Label(coord_frame, text="Y:").pack(side=tk.LEFT)
        y_var = tk.StringVar(value="100")
        ttk.Entry(coord_frame, textvariable=y_var, width=8).pack(side=tk.LEFT, padx=(5, 0))

        # 超时设置
        timeout_frame = ttk.LabelFrame(wait_window, text="超时设置", padding="10")
        timeout_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(timeout_frame, text="超时时间(秒):").pack(side=tk.LEFT)
        timeout_var = tk.StringVar(value="30")
        ttk.Entry(timeout_frame, textvariable=timeout_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 结果显示
        result_var = tk.StringVar(value="等待执行...")
        ttk.Label(wait_window, textvariable=result_var).pack(pady=10)

        def execute_wait():
            wait_type = wait_type_var.get()
            target = target_var.get().strip()

            try:
                timeout = int(timeout_var.get())

                result_var.set("等待中...")
                wait_window.update()

                if wait_type == "image":
                    if not target:
                        messagebox.showwarning("警告", "请输入图像模板名称")
                        return
                    result = self.rpa_core.wait_for_image_appear(target, timeout)
                    if result:
                        result_var.set(f"图像出现在: ({result[0]}, {result[1]})")
                    else:
                        result_var.set("等待超时，图像未出现")

                elif wait_type == "text":
                    if not target:
                        messagebox.showwarning("警告", "请输入要等待的文字")
                        return
                    result = self.rpa_core.wait_for_text_appear(target, timeout)
                    if result:
                        result_var.set(f"文字出现在: ({result[0]}, {result[1]})")
                    else:
                        result_var.set("等待超时，文字未出现")

                elif wait_type == "color":
                    x = int(x_var.get())
                    y = int(y_var.get())
                    result = self.rpa_core.wait_for_color_change(x, y, timeout)
                    if result:
                        result_var.set(f"颜色已变化: ({x}, {y})")
                    else:
                        result_var.set("等待超时，颜色未变化")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        ttk.Button(wait_window, text="开始等待", command=execute_wait).pack(pady=10)

    def open_script_editor(self):
        """打开脚本编辑器"""
        editor_window = tk.Toplevel(self.root)
        editor_window.title("RPA脚本编辑器")
        editor_window.geometry("800x600")

        # 创建菜单
        menubar = tk.Menu(editor_window)
        editor_window.config(menu=menubar)

        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)

        # 脚本编辑区
        edit_frame = ttk.Frame(editor_window)
        edit_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        script_text = tk.Text(edit_frame, wrap=tk.NONE, font=("Consolas", 10))

        # 滚动条
        v_scrollbar = ttk.Scrollbar(edit_frame, orient=tk.VERTICAL, command=script_text.yview)
        h_scrollbar = ttk.Scrollbar(edit_frame, orient=tk.HORIZONTAL, command=script_text.xview)
        script_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        script_text.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        edit_frame.grid_rowconfigure(0, weight=1)
        edit_frame.grid_columnconfigure(0, weight=1)

        # 示例脚本
        example_script = '''# RPA脚本示例
# 这是一个Python脚本，可以使用RPA核心功能

# 等待图像出现并点击
if rpa.wait_for_image_appear("login_button", timeout=10):
    rpa.click_image_smart("login_button")

# 智能文字输入
rpa.type_text_smart("用户名", clear_first=True)

# 等待文字出现并点击
if rpa.wait_for_text_appear("登录", timeout=5):
    rpa.click_text_smart("登录")

# 等待颜色变化
rpa.wait_for_color_change(100, 100, timeout=15)

print("脚本执行完成")
'''

        script_text.insert("1.0", example_script)

        # 执行按钮
        button_frame = ttk.Frame(editor_window)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

        def execute_script():
            script_content = script_text.get("1.0", tk.END)
            try:
                # 创建执行环境
                exec_globals = {
                    'rpa': self.rpa_core,
                    'print': lambda *args: messagebox.showinfo("脚本输出", " ".join(map(str, args))),
                    'time': time,
                    'pyautogui': pyautogui
                }

                exec(script_content, exec_globals)
                messagebox.showinfo("成功", "脚本执行完成")

            except Exception as e:
                messagebox.showerror("错误", f"脚本执行失败:\n{e}")

        ttk.Button(button_frame, text="执行脚本", command=execute_script).pack(side=tk.LEFT, padx=(0, 5))

        def save_script():
            filename = filedialog.asksaveasfilename(
                defaultextension=".py",
                filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(script_text.get("1.0", tk.END))
                messagebox.showinfo("成功", f"脚本已保存: {filename}")

        def load_script():
            filename = filedialog.askopenfilename(
                filetypes=[("Python文件", "*.py"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                script_text.delete("1.0", tk.END)
                script_text.insert("1.0", content)
                messagebox.showinfo("成功", f"脚本已加载: {filename}")

        file_menu.add_command(label="保存", command=save_script)
        file_menu.add_command(label="加载", command=load_script)

        ttk.Button(button_frame, text="保存脚本", command=save_script).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="加载脚本", command=load_script).pack(side=tk.LEFT, padx=(0, 5))

    def on_closing(self):
        """窗口关闭事件"""
        self.running = False
        if self.rpa_core.recording:
            self.rpa_core.stop_recording()
        if self.rpa_core.playing:
            self.rpa_core.stop_playing()
        self.root.destroy()


def main():
    root = tk.Tk()
    app = RPAGUI(root)
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    root.mainloop()


if __name__ == "__main__":
    main()
