# RPA自动化操作工具

这是一个基于Python的RPA（机器人流程自动化）工具，可以录制和回放鼠标、键盘操作，实现桌面应用程序的自动化。

## 功能特性

### 基础功能
- 🎯 **操作录制**: 录制鼠标点击、移动、滚轮和键盘操作
- 🔄 **操作回放**: 精确回放录制的操作序列
- 🖥️ **图形界面**: 直观的GUI界面，方便操作和管理
- 📁 **脚本管理**: 保存和加载操作脚本
- 🔧 **灵活配置**: 可调节播放速度、重复次数等参数

### 高级功能 ⭐
- 🔍 **图像识别**: 基于OpenCV的模板匹配，不依赖坐标定位
- 📝 **文字识别**: 集成EasyOCR和Tesseract，支持中英文识别
- ⏳ **智能等待**: 等待图像出现、文字出现、颜色变化等条件
- 🎯 **智能点击**: 根据图像或文字内容进行精确点击
- 📋 **模板管理**: 保存和管理屏幕区域模板
- 🔄 **拖拽操作**: 智能拖拽功能，支持图像到图像的拖拽
- 📜 **脚本编辑器**: 内置Python脚本编辑器，支持高级自动化逻辑
- 🔍 **滚动查找**: 自动滚动查找目标元素
- 📊 **批量操作**: 支持批量处理相同类型的元素

### 实用工具
- 📸 **截屏工具**: 内置截屏功能，辅助操作定位
- 🪟 **窗口管理**: 查看和激活系统窗口
- 📍 **坐标显示**: 实时显示鼠标位置

## 安装要求

### 系统要求
- Python 3.7+
- Windows 10/11 (推荐)
- 可选：Tesseract OCR (用于文字识别)

### 安装依赖

#### 自动安装（推荐）
```bash
# 双击运行启动脚本，会自动安装所有依赖
start_rpa.bat
```

#### 手动安装
```bash
# 基础依赖
pip install pyautogui pillow opencv-python pynput keyboard pygetwindow psutil

# 高级功能依赖（可选）
pip install easyocr pytesseract scikit-image matplotlib

# 或者一次性安装所有依赖
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序
```bash
python rpa_gui.py
```

### 2. 录制操作
1. 点击"开始录制"按钮
2. 执行你想要自动化的操作（鼠标点击、键盘输入等）
3. 点击"停止录制"按钮完成录制

### 3. 播放操作
1. 设置重复次数和播放速度
2. 点击"开始播放"按钮
3. 程序将自动重复执行录制的操作

### 4. 保存和加载脚本
- 使用"保存操作"将录制的操作保存为JSON文件
- 使用"加载操作"从文件中加载之前保存的操作

### 5. 使用高级功能
- **图像识别测试**: 测试模板匹配功能
- **文字识别测试**: 测试OCR文字识别
- **模板管理**: 保存和管理屏幕区域模板
- **智能点击**: 根据图像或文字进行点击
- **智能等待**: 等待特定条件出现
- **脚本编辑器**: 编写和执行高级自动化脚本

## 主要功能模块

### 录制功能
- **鼠标操作**: 记录点击位置、按钮类型、按下/释放状态
- **键盘操作**: 记录按键的按下和释放
- **鼠标移动**: 记录鼠标移动轨迹（可配置阈值）
- **滚轮操作**: 记录鼠标滚轮滚动

### 播放功能
- **精确时间**: 按照录制时的时间间隔播放
- **速度控制**: 可调节播放速度（0.1x - 5.0x）
- **重复播放**: 支持设置重复次数
- **实时停止**: 播放过程中可随时停止

### 高级识别功能
- **图像识别**:
  - 基于OpenCV的模板匹配算法
  - 支持多个匹配结果
  - 可调节置信度阈值
  - 自动缓存模板提高性能
- **文字识别**:
  - 集成EasyOCR引擎，支持中英文
  - 备用Tesseract OCR支持
  - 返回文字内容、位置和置信度
  - 支持指定区域识别

### 智能操作功能
- **智能点击**:
  - `click_image_smart()` - 根据图像模板点击
  - `click_text_smart()` - 根据文字内容点击
- **智能等待**:
  - `wait_for_image_appear()` - 等待图像出现
  - `wait_for_text_appear()` - 等待文字出现
  - `wait_for_color_change()` - 等待颜色变化
- **智能输入**:
  - `type_text_smart()` - 智能文字输入，支持清空
- **高级操作**:
  - `scroll_to_find_image()` - 滚动查找图像
  - `drag_image_to_image()` - 图像间拖拽

### 模板管理
- **模板保存**: 保存屏幕区域为可重用模板
- **模板加载**: 自动加载templates目录中的模板
- **模板管理**: 列出、删除、重命名模板
- **模板缓存**: 自动缓存提高识别速度

### 实用工具
- **截屏工具**: 全屏或区域截图
- **鼠标位置**: 实时显示鼠标坐标
- **窗口列表**: 查看和激活系统窗口
- **脚本编辑器**: 内置Python脚本编辑和执行环境

## 配置文件说明

`config.json` 文件包含以下配置项：

```json
{
    "rpa_settings": {
        "failsafe": true,                    // 安全模式（鼠标移到左上角停止）
        "pause": 0.1,                       // 操作间隔时间
        "confidence_threshold": 0.8,        // 图像识别置信度
        "mouse_move_threshold": 0.5         // 鼠标移动记录阈值
    },
    "recording_settings": {
        "record_mouse_moves": true,         // 是否记录鼠标移动
        "record_keyboard": true,            // 是否记录键盘操作
        "auto_save": false,                 // 自动保存
        "auto_save_interval": 300           // 自动保存间隔（秒）
    },
    "playback_settings": {
        "default_repeat_count": 1,          // 默认重复次数
        "default_speed_multiplier": 1.0,    // 默认播放速度
        "stop_on_error": true               // 出错时停止
    }
}
```

## 命令行使用

也可以直接使用核心模块：

```python
from rpa_core import RPACore

# 创建RPA实例
rpa = RPACore()

# 开始录制
rpa.start_recording()
# ... 执行操作 ...
rpa.stop_recording()

# 播放操作
rpa.play_actions(repeat_count=3, speed_multiplier=1.5)

# 保存和加载
rpa.save_actions("my_script.json")
rpa.load_actions("my_script.json")
```

## 安全特性

- **故障安全**: 鼠标移动到屏幕左上角可紧急停止所有操作
- **操作间隔**: 每个操作之间有可配置的延迟时间
- **错误处理**: 播放过程中的错误会被捕获和记录

## 应用场景

- **重复性任务**: 自动化日常重复的桌面操作
- **数据录入**: 批量数据输入和表单填写
- **软件测试**: 自动化GUI测试流程
- **系统管理**: 批量系统配置和管理任务

## 注意事项

- 录制前确保目标应用程序处于正确状态
- 播放时确保屏幕分辨率和窗口位置与录制时一致
- 对于需要精确定位的操作，建议使用图像识别功能
- 长时间录制可能产生大量数据，建议适当分段

## 故障排除

### 常见问题

1. **权限问题**
   - 以管理员身份运行程序
   - 确保Python有访问输入设备的权限

2. **操作不准确**
   - 检查屏幕分辨率是否一致
   - 调整操作间隔时间
   - 使用图像识别辅助定位

3. **录制失败**
   - 检查依赖包是否正确安装
   - 确认系统兼容性

## 许可证

MIT License
